using FluentResults.Extensions.AspNetCore;
using MediatR;
using TeamFlow.Identity.Application.Contracts.Dto.User;
using TeamFlow.Identity.Core.Errors;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TeamFlow.Identity.Api.Api.Responses.Base;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;
using TeamFlow.Identity.Application.Contracts.Features.Queries.User;
using UserRole = TeamFlow.Shared.Contracts.Enums.UserRole;

namespace TeamFlow.Identity.Api.Controllers;

[Authorize]
[ApiController]
[Route("/api/[controller]")]
public sealed class UsersController : ControllerBase
{
    private readonly ISender _sender;

    public UsersController(ISender sender)
    {
        ArgumentNullException.ThrowIfNull(sender);
        _sender = sender;
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetUserByIdQuery(id), cancellationToken);
        return result.ToActionResult();
    }
    
    

    [HttpGet("login/{login}")]
    public async Task<IActionResult> GetByLoginAsync(string login, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetUserByLoginQuery(login), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet("email/{email}")]
    public async Task<IActionResult> GetByEmail(string email, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetUserByEmailQuery(email), cancellationToken);
        return result.ToActionResult();
    }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPost]
    public async Task<IActionResult> CreateAsync(
        [FromBody] CreateUserCommand request,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(request, cancellationToken);
        return result.ToActionResult();
    }

    [Authorize]
    [HttpPatch("{id:guid}")]
    public async Task<IActionResult> UpdateAsync(
        Guid id,
        [FromBody] UpdateUserRequest request)
    {
        var result = await _sender.Send(new UpdateUserCommand(id, request.Login, request.Email,
            request.Password, request.Role, request.PositionId));

        return result.ToActionResult();
    }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new DeleteUserCommand(id), cancellationToken);
        return result.ToActionResult();
    }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPatch("{id:guid}/position")]
    public async Task<IActionResult> UpdatePosition(
        Guid id,
        [FromBody] UpdateUserPositionRequest request)
    {
        var result = await _sender.Send(new UpdateUserPositionCommand(id, request.PositionId));
        return result.ToActionResult();
    }

    [Authorize]
    [HttpPatch("{id:guid}/online-status")]
    public async Task<IActionResult> UpdateOnlineStatus(
        Guid id,
        [FromBody] UpdateUserOnlineStatusRequest request)
    {
        var result = await _sender.Send(new UpdateUserOnlineStatusCommand(id, request.OnlineStatus));
        return result.ToActionResult();
    }
}