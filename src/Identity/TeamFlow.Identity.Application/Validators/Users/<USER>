using FluentValidation;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Application.Validators.Users;

public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    public CreateUserCommandValidator()
    {
        RuleFor(u => u.Login)
            .NotEmpty()
            .Length(User.MinLogin<PERSON>ength, User.MaxLoginLength);
            // .MustAsync(async (login, cancellationToken) =>
            // {
            //     var user =  await _usersRepository.GetByLoginAsync(login, cancellationToken);
            //     return user is null;
            // });

        RuleFor(u => u.Email)
            .NotEmpty()
            .EmailAddress()
            .MaximumLength(User.MaxEmailLength);

        RuleFor(u => u.Password)
            .NotEmpty()
            .Length(User.MinPasswordLength, User.MaxPasswordLength)
            .Matches(@"^(?!.*\s)[\x21-\x7E]+$");
            // .Must(x => Regex.IsMatch(x, @"^(?!.*\s)[\x21-\x7E]+$"));

        RuleFor(u => u.Role)
            .NotNull()
            .IsInEnum();

        RuleFor(u => u.PositionId)
            .NotEmpty();
    }
}