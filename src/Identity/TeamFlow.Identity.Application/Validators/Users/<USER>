using FluentValidation;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Application.Validators.Users;

public class UpdateUserCommandValidator : AbstractValidator<UpdateUserCommand>
{
    public UpdateUserCommandValidator()
    {
        // Логин: если предоставлен, то не пустой и в пределах допустимой длины
        RuleFor(u => u.Login)
            .NotEmpty()
            .When(u => !string.IsNullOrEmpty(u.<PERSON>gin)) // Проверка, если логин передан
            .Length(User.MinLoginLength, User.MaxLoginLength)
            .When(u => !string.IsNullOrEmpty(u.Login)); // Проверка длины, если логин передан

        // Электронная почта: если предоставлена, то не пустая и является допустимым email
        RuleFor(u => u.Email)
            .NotEmpty()
            .When(u => !string.IsNullOrEmpty(u.Email)) // Проверка, если email передан
            .EmailAddress()
            .When(u => !string.IsNullOrEmpty(u.Email)) // Проверка формата email, если email передан
            .MaximumLength(User.MaxEmailLength);

        // Пароль: если предоставлен, то проверка на длину и соответствие шаблону
        RuleFor(u => u.Password)
            .Length(User.MinPasswordLength, User.MaxPasswordLength)
            .Matches(@"^(?!.*\s)[\x21-\x7E]+$")
            .When(u => !string.IsNullOrEmpty(u.Password)); // Проверка, если пароль передан

        // Роль: должна быть задана и соответствовать существующему enum
        RuleFor(u => u.Role)
            .NotNull()
            .IsInEnum()
            .When(u => u.Role.HasValue); // Проверка роли только если она передана

        // Позиция: если передана, то не пуста
        RuleFor(u => u.PositionId)
            .NotEmpty()
            .When(u => u.PositionId.HasValue); // Проверка на наличие позиции только если передана
    }
}