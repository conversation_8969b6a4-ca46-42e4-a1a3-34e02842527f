using System.Net;
using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;
using TeamFlow.Identity.Application.Contracts.Services;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Commands.Users;

public sealed class ChangePasswordCommandHandler : IRequestHandler<ChangePasswordCommand, Result>
{
    private readonly IUsersRepository _usersRepository;
    private readonly IHashDataService _hashDataService;
    private readonly IValidator<ChangePasswordCommand> _validator;
    private readonly ILogger<ChangePasswordCommandHandler> _logger;

    public ChangePasswordCommandHandler(
        IUsersRepository usersRepository,
        IHashDataService hashDataService,
        IValidator<ChangePasswordCommand> validator,
        ILogger<ChangePasswordCommandHandler> logger)
    {
        ArgumentNullException.ThrowIfNull(usersRepository);
        ArgumentNullException.ThrowIfNull(hashDataService);
        ArgumentNullException.ThrowIfNull(validator);
        ArgumentNullException.ThrowIfNull(logger);

        _usersRepository = usersRepository;
        _hashDataService = hashDataService;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result> Handle(ChangePasswordCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(User), validationResult));
        }

        var user = await _usersRepository.GetOneAsync(x => x.Id == request.UserId, cancellationToken);
        if (user is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(User), request.UserId));
        }

        // Verify old password
        var isValidOldPassword = _hashDataService.VerifyData(request.OldPassword, user.PasswordHash);
        if (!isValidOldPassword)
        {
            return Result.Fail(ErrorsFactory.Custom("Invalid old password", HttpStatusCode.BadRequest));
        }

        // Hash new password and update user
        user.PasswordHash = _hashDataService.HashData(request.NewPassword);
        user.LastPasswordChangeAt = DateTime.UtcNow;
        user.UpdatedAt = DateTime.UtcNow;

        await _usersRepository.UpdateAsync(user, cancellationToken);

        _logger.LogInformation("Password changed successfully for user {UserId}", request.UserId);

        return Result.Ok();
    }
}
