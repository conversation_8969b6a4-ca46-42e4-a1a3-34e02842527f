using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;
using TeamFlow.Identity.Application.Contracts.Services;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Services.Interfaces;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Enums.Settings;
using TeamFlow.Identity.Core.Repositories;
using TeamFlow.Shared.Repositories.Repositories;

namespace TeamFlow.Identity.Application.Features.Commands.Users;

public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, Result<Guid>>
{
    private readonly IGenericRepository<User, Guid> _usersRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IValidator<CreateUserCommand> _validator;
    private readonly ILogger<CreateUserCommandHandler> _logger;
    private readonly IHashDataService _hashDataService;

    public CreateUserCommandHandler(IGenericRepository<User, Guid> usersRepository,
        IValidator<CreateUserCommand> validator, ILogger<CreateUserCommandHandler> logger,
        IHashDataService hashDataService, IUnitOfWork unitOfWork)
    {
        _usersRepository = usersRepository;
        _validator = validator;
        _logger = logger;
        _hashDataService = hashDataService;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(User), validationResult));
        }

        var isUserExists = await _usersRepository.FindAnyAsync(
            u => u.Login == request.Login || u.Email == request.Email,
            cancellationToken);

        if (isUserExists)
        {
            return Result.Fail(ErrorsFactory.AlreadyExists(nameof(User), nameof(request.Login), request.Login));
        }

        var id = Guid.CreateVersion7();
        
        var userAvatar = $"https://api.dicebear.com/7.x/avataaars/svg?seed={id}";

        var user = new User
        {
            Id = id,
            Login = request.Login,
            Email = request.Email,
            FirstName = request.FirstName,
            LastName = request.LastName,
            PasswordHash = _hashDataService.HashData(request.Password),
            Role = request.Role,
            PositionId = request.PositionId,
            AvatarUrl = userAvatar,
            CreatedAt = DateTime.Now
        };
        
        var settings = new UserSettings
        {
            UserId = user.Id,
            EmailNotification = false,
            SystemNotification = false,
            EventReminder = false,
            PanelMode = SidebarPanelMode.Extended,
            UiTheme = UiTheme.System,
            Language = Language.Ru
        };

        user.Settings = settings;

        await using var transaction = await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            await _usersRepository.AddAsync(user, cancellationToken);
            await transaction.CommitAsync(cancellationToken);
            return Result.Ok(user.Id);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error creating user {UserId}", user.Id);
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }
}