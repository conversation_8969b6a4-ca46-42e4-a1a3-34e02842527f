using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Persistence.Database.Config;

public class UserSettingsConfiguration : IEntityTypeConfiguration<UserSettings>
{
    public void Configure(EntityTypeBuilder<UserSettings> builder)
    {
        builder.HasKey(s => s.Id);
        
        // Уникальный индекс на UserId для обеспечения связи один-к-одному
        builder.HasIndex(s => s.UserId).IsUnique();
    }
}