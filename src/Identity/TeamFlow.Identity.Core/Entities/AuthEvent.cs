using System.ComponentModel.DataAnnotations;
using TeamFlow.Identity.Core.Enums.Jwt;
using TeamFlow.Shared.Repositories.Entities;

namespace TeamFlow.Identity.Core.Entities;

public class AuthEvent : Entity<Guid>
{
    public const byte MaxIpAddressLength = 45;
    public const byte MaxCountryLength = 2;
    public const byte MaxCityLength = 100;
    public const byte MaxDeviceTypeLength = 50;
    public const byte MaxDeviceNameLength = 100;
    public const byte MaxOsNameLength = 50;
    public const byte MaxOsVersionLength = 20;
    public const byte MaxBrowserNameLength = 50;
    public const byte MaxBrowserVersionLength = 20;
    
    public Guid UserId { get; set; }
    
    public AuthEvenType EventType { get; set; }
    
    // IPv6 поддержка
    [MaxLength(MaxIpAddressLength)]
    public string IpAddress { get; set; }
    
    [MaxLength(MaxCountryLength)]
    public string? Country { get; set; } // RU, US
    
    [MaxLength(MaxCityLength)]
    public string? City { get; set; }
    
    // public string? UserAgent { get; set; } // Полный User-Agent

    [MaxLength(MaxDeviceTypeLength)]
    public string? DeviceType { get; set; } // mobile, desktop, tablet
    
    [MaxLength(MaxDeviceNameLength)]
    public string? DeviceName { get; set; } // "iPhone 15", "Chrome on Windows"
    
    [MaxLength(MaxOsNameLength)]
    public string? OsName { get; set; } // iOS, Windows, Android, macOS
    
    [MaxLength(MaxOsVersionLength)]
    public string? OsVersion { get; set; } // 17.0, Windows 11
    
    [MaxLength(MaxBrowserNameLength)]
    public string? BrowserName { get; set; } // Chrome, Safari, Firefox
    
    [MaxLength(MaxBrowserVersionLength)]
    public string? BrowserVersion { get; set; } // 120
    
    public DateTime LastActivity { get; set; }
    public bool IsActive { get; set; }
    
    public User User { get; set; }
}