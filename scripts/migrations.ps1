param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("add", "update", "remove")]
    [string]$Command,
    
    [Parameter(Mandatory=$false)]
    [string]$Name,
    
    [Parameter(Mandatory=$false)]
    [string]$Context,
    
    [Parameter(Mandatory=$false)]
    [string]$Project
)

$ErrorActionPreference = "Stop"

# Определение проектов с миграциями
$MigrationProjects = @{
    "Tasks" = "TeamFlow.Tasks.Infrastructure"
    "Identity" = "TeamFlow.Identity.Persistence"
    "Gateway" = "TeamFlow.Gateway.Infrastructure"
}

function Get-ProjectPath {
    param([string]$Context)
    
    if ($Project) {
        return $Project
    }
    
    if ($Context -and $MigrationProjects.ContainsKey($Context)) {
        return $MigrationProjects[$Context]
    }
    
    Write-Error "Context '$Context' not found. Available contexts: $($MigrationProjects.Keys -join ', ')"
    exit 1
}

function Add-Migration {
    param(
        [string]$MigrationName,
        [string]$ProjectPath
    )
    
    Write-Host "Adding new migration: $MigrationName to project: $ProjectPath"
    dotnet ef migrations add $MigrationName --project $ProjectPath
}

function Update-Database {
    param([string]$ProjectPath)
    
    Write-Host "Applying migrations to database for project: $ProjectPath"
    dotnet ef database update --project $ProjectPath
}

function Remove-Migration {
    param([string]$ProjectPath)
    
    Write-Host "Removing last migration from project: $ProjectPath"
    dotnet ef migrations remove --project $ProjectPath
}

# Получаем путь к проекту
$ProjectPath = Get-ProjectPath -Context $Context

switch ($Command) {
    "add" {
        if ([string]::IsNullOrEmpty($Name)) {
            Write-Error "Migration name is required for 'add' command"
            exit 1
        }
        Add-Migration -MigrationName $Name -ProjectPath $ProjectPath
    }
    "update" {
        Update-Database -ProjectPath $ProjectPath
    }
    "remove" {
        Remove-Migration -ProjectPath $ProjectPath
    }
} 